#!/usr/bin/env node

/**
 * 🧪 Script de Test Phase 4 - Excellence Opérationnelle
 * Validation complète de l'implémentation Phase 4
 */

const axios = require('axios');
const chalk = require('chalk');

// Configuration
const BASE_URL = 'http://localhost:3000';
const API_PREFIX = '/api/v1';
const TIMEOUT = 10000;

// Couleurs pour les logs
const log = {
  success: (msg) => console.log(chalk.green('✅ ' + msg)),
  error: (msg) => console.log(chalk.red('❌ ' + msg)),
  warning: (msg) => console.log(chalk.yellow('⚠️ ' + msg)),
  info: (msg) => console.log(chalk.blue('ℹ️ ' + msg)),
  title: (msg) => console.log(chalk.cyan.bold('\n🎯 ' + msg)),
};

// Client HTTP configuré
const client = axios.create({
  baseURL: BASE_URL + API_PREFIX,
  timeout: TIMEOUT,
  validateStatus: () => true, // Ne pas rejeter sur les codes d'erreur
});

/**
 * Tests de la documentation interactive
 */
async function testInteractiveDocumentation() {
  log.title('Test Documentation Interactive');

  try {
    // Test de l'accès à Swagger UI
    const swaggerResponse = await axios.get(`${BASE_URL}/api/docs`, {
      timeout: TIMEOUT,
      validateStatus: () => true,
    });

    if (swaggerResponse.status === 200) {
      log.success('Swagger UI accessible');
    } else {
      log.warning(`Swagger UI retourne le code ${swaggerResponse.status}`);
    }

    // Test de l'endpoint d'information documentation
    const docInfoResponse = await client.get('/phase4/documentation/info');
    
    if (docInfoResponse.status === 200) {
      log.success('Endpoint documentation/info accessible');
      log.info(`Version: ${docInfoResponse.data.version}`);
      log.info(`Features: ${docInfoResponse.data.features.length} fonctionnalités`);
    } else {
      log.error(`Documentation info inaccessible: ${docInfoResponse.status}`);
    }

  } catch (error) {
    log.error(`Erreur test documentation: ${error.message}`);
  }
}

/**
 * Tests des outils de debug
 */
async function testDebugTools() {
  log.title('Test Outils de Debug');

  try {
    // Test du rapport de performance
    const perfResponse = await client.get('/phase4/debug/performance-report');
    
    if (perfResponse.status === 200) {
      log.success('Rapport de performance accessible');
      const report = perfResponse.data;
      log.info(`Total opérations: ${report.summary.totalOperations}`);
      log.info(`Durée moyenne: ${report.summary.averageDuration.toFixed(2)}ms`);
      log.info(`Recommandations: ${report.recommendations.length}`);
    } else {
      log.error(`Rapport de performance inaccessible: ${perfResponse.status}`);
    }

    // Test du profiling
    const profilingStartResponse = await client.post('/phase4/debug/start-profiling', {
      operationName: 'test-operation'
    });

    if (profilingStartResponse.status === 200 || profilingStartResponse.status === 201) {
      log.success('Profiling démarré');
      
      // Attendre un peu puis arrêter le profiling
      setTimeout(async () => {
        try {
          const profilingEndResponse = await client.post('/phase4/debug/end-profiling', {
            operationName: 'test-operation',
            startMark: profilingStartResponse.data.markName
          });

          if (profilingEndResponse.status === 200 || profilingEndResponse.status === 201) {
            log.success('Profiling arrêté');
          } else {
            log.warning(`Arrêt profiling: ${profilingEndResponse.status}`);
          }
        } catch (error) {
          log.warning(`Erreur arrêt profiling: ${error.message}`);
        }
      }, 1000);
    } else {
      log.error(`Démarrage profiling échoué: ${profilingStartResponse.status}`);
    }

  } catch (error) {
    log.error(`Erreur test debug tools: ${error.message}`);
  }
}

/**
 * Tests de l'interface d'administration
 */
async function testAdminInterface() {
  log.title('Test Interface d\'Administration');

  try {
    // Test du dashboard admin
    const dashboardResponse = await client.get('/phase4/admin/dashboard');
    
    if (dashboardResponse.status === 200) {
      log.success('Dashboard admin accessible');
      const dashboard = dashboardResponse.data;
      log.info(`Utilisateurs actifs: ${dashboard.overview.activeUsers}`);
      log.info(`Retraites actives: ${dashboard.overview.activeRetreats}`);
      log.info(`Alertes système: ${dashboard.alerts.length}`);
    } else {
      log.error(`Dashboard admin inaccessible: ${dashboardResponse.status}`);
    }

    // Test de la gestion des utilisateurs
    const userMgmtResponse = await client.get('/phase4/admin/user-management');
    
    if (userMgmtResponse.status === 200) {
      log.success('Gestion utilisateurs accessible');
      const userMgmt = userMgmtResponse.data;
      log.info(`Total utilisateurs: ${userMgmt.totalCount}`);
      log.info(`Actions bulk: ${userMgmt.bulkActions.length}`);
    } else {
      log.warning(`Gestion utilisateurs: ${userMgmtResponse.status}`);
    }

    // Test de la configuration système
    const configResponse = await client.get('/phase4/admin/system-config');
    
    if (configResponse.status === 200) {
      log.success('Configuration système accessible');
      const config = configResponse.data;
      log.info(`Version app: ${config.application.version}`);
      log.info(`Environnement: ${config.application.environment}`);
    } else {
      log.warning(`Configuration système: ${configResponse.status}`);
    }

  } catch (error) {
    log.error(`Erreur test admin interface: ${error.message}`);
  }
}

/**
 * Tests du versioning API
 */
async function testApiVersioning() {
  log.title('Test API Versioning');

  try {
    // Test des informations de versioning
    const versionInfoResponse = await client.get('/phase4/versioning/info');
    
    if (versionInfoResponse.status === 200) {
      log.success('Informations versioning accessibles');
      const versionInfo = versionInfoResponse.data;
      log.info(`Version actuelle: ${versionInfo.currentVersion}`);
      log.info(`Versions supportées: ${versionInfo.supportedVersions.length}`);
      log.info(`Versions dépréciées: ${versionInfo.deprecatedVersions.length}`);
    } else {
      log.error(`Informations versioning inaccessibles: ${versionInfoResponse.status}`);
    }

    // Test de validation de version
    const validationResponse = await client.post('/phase4/versioning/validate', {
      version: '3.8.1'
    });

    if (validationResponse.status === 200 || validationResponse.status === 201) {
      log.success('Validation de version fonctionnelle');
      const validation = validationResponse.data;
      log.info(`Version valide: ${validation.isValid}`);
      log.info(`Version supportée: ${validation.isSupported}`);
    } else {
      log.warning(`Validation version: ${validationResponse.status}`);
    }

    // Test des statistiques d'utilisation
    const statsResponse = await client.get('/phase4/versioning/usage-stats');
    
    if (statsResponse.status === 200) {
      log.success('Statistiques d\'utilisation accessibles');
      const stats = statsResponse.data;
      log.info(`Total requêtes: ${stats.totalRequests}`);
      log.info(`Distributions: ${Object.keys(stats.versionDistribution).length} versions`);
    } else {
      log.warning(`Statistiques utilisation: ${statsResponse.status}`);
    }

    // Test du plan de migration
    const migrationResponse = await client.post('/phase4/versioning/migration-plan', {
      currentVersion: '3.0.0',
      targetVersion: '3.8.1'
    });

    if (migrationResponse.status === 200 || migrationResponse.status === 201) {
      log.success('Plan de migration généré');
      const migration = migrationResponse.data;
      log.info(`Temps estimé: ${migration.estimatedTime}`);
      log.info(`Priorité: ${migration.priority}`);
    } else {
      log.warning(`Plan de migration: ${migrationResponse.status}`);
    }

  } catch (error) {
    log.error(`Erreur test API versioning: ${error.message}`);
  }
}

/**
 * Tests globaux Phase 4
 */
async function testPhase4Global() {
  log.title('Test Global Phase 4');

  try {
    // Test du statut Phase 4
    const statusResponse = await client.get('/phase4/status');
    
    if (statusResponse.status === 200) {
      log.success('Statut Phase 4 accessible');
      const status = statusResponse.data;
      log.info(`Phase: ${status.phase}`);
      log.info(`Version: ${status.version}`);
      log.info(`Statut: ${status.status}`);
      log.info(`Composants: ${Object.keys(status.components).length}`);
      log.info(`Progrès: ${status.metrics.implementationProgress}`);
    } else {
      log.error(`Statut Phase 4 inaccessible: ${statusResponse.status}`);
    }

    // Test du health check Phase 4
    const healthResponse = await client.get('/phase4/health');
    
    if (healthResponse.status === 200) {
      log.success('Health check Phase 4 accessible');
      const health = healthResponse.data;
      log.info(`Statut global: ${health.overall}`);
      log.info(`Composants: ${Object.keys(health.components).length}`);
      log.info(`Recommandations: ${health.recommendations.length}`);
    } else {
      log.warning(`Health check Phase 4: ${healthResponse.status}`);
    }

  } catch (error) {
    log.error(`Erreur test global Phase 4: ${error.message}`);
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log(chalk.cyan.bold('🧪 TESTS PHASE 4 - EXCELLENCE OPÉRATIONNELLE'));
  console.log(chalk.gray('Testing URL: ' + BASE_URL + API_PREFIX));
  console.log(chalk.gray('Timeout: ' + TIMEOUT + 'ms\n'));

  // Vérifier que le serveur est accessible
  try {
    const healthResponse = await client.get('/health');
    if (healthResponse.status === 200) {
      log.success('Serveur accessible');
    } else {
      log.warning(`Serveur retourne: ${healthResponse.status}`);
    }
  } catch (error) {
    log.error(`Serveur inaccessible: ${error.message}`);
    log.error('Assurez-vous que le serveur est démarré sur ' + BASE_URL);
    process.exit(1);
  }

  // Exécuter tous les tests
  await testInteractiveDocumentation();
  await testDebugTools();
  await testAdminInterface();
  await testApiVersioning();
  await testPhase4Global();

  console.log(chalk.cyan.bold('\n🎉 TESTS PHASE 4 TERMINÉS'));
  console.log(chalk.gray('Vérifiez les logs ci-dessus pour les détails'));
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  log.error(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécution
main().catch((error) => {
  log.error(`Erreur principale: ${error.message}`);
  process.exit(1);
});
