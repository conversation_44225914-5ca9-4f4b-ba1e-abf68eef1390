import { Injectable, Logger, VersioningType } from '@nestjs/common';
import { INestApplication } from '@nestjs/common';
import * as semver from 'semver';

/**
 * 🔄 Service de Versioning API Sémantique
 * Phase 4 - Excellence Opérationnelle
 */
@Injectable()
export class ApiVersioningService {
  private readonly logger = new Logger(ApiVersioningService.name);
  private readonly supportedVersions = ['1.0.0', '2.0.0', '3.0.0', '3.8.1'];
  private readonly deprecatedVersions = ['1.0.0'];
  private readonly currentVersion = '3.8.1';

  /**
   * 🚀 Configurer le versioning de l'API
   */
  setupApiVersioning(app: INestApplication): void {
    this.logger.log('Setting up API versioning...');

    // Configuration du versioning par header
    app.enableVersioning({
      type: VersioningType.HEADER,
      header: 'X-API-Version',
      defaultVersion: this.currentVersion,
    });

    // Configuration du versioning par URI
    app.enableVersioning({
      type: VersioningType.URI,
      prefix: 'v',
      defaultVersion: '3',
    });

    this.logger.log('✅ API versioning configured');
  }

  /**
   * 📋 Obtenir les informations de versioning
   */
  getVersioningInfo(): any {
    return {
      currentVersion: this.currentVersion,
      supportedVersions: this.supportedVersions,
      deprecatedVersions: this.deprecatedVersions,
      versioningStrategy: {
        type: 'Semantic Versioning (SemVer)',
        format: 'MAJOR.MINOR.PATCH',
        description: 'MAJOR: breaking changes, MINOR: new features, PATCH: bug fixes',
      },
      migrationGuides: this.getMigrationGuides(),
      backwardCompatibility: this.getBackwardCompatibilityInfo(),
    };
  }

  /**
   * 🔍 Valider une version
   */
  validateVersion(version: string): {
    isValid: boolean;
    isSupported: boolean;
    isDeprecated: boolean;
    message: string;
  } {
    const isValid = semver.valid(version) !== null;
    const isSupported = this.supportedVersions.includes(version);
    const isDeprecated = this.deprecatedVersions.includes(version);

    let message = '';
    if (!isValid) {
      message = 'Version format invalide. Utilisez le format SemVer (ex: 3.8.1)';
    } else if (!isSupported) {
      message = `Version ${version} non supportée. Versions supportées: ${this.supportedVersions.join(', ')}`;
    } else if (isDeprecated) {
      message = `Version ${version} dépréciée. Migrez vers la version ${this.currentVersion}`;
    } else {
      message = `Version ${version} valide et supportée`;
    }

    return {
      isValid,
      isSupported,
      isDeprecated,
      message,
    };
  }

  /**
   * 📚 Obtenir les guides de migration
   */
  private getMigrationGuides(): any {
    return {
      'v1.0.0-to-v2.0.0': {
        title: 'Migration de v1.0.0 vers v2.0.0',
        breakingChanges: [
          'Changement du format de réponse des endpoints /users',
          'Suppression de l\'endpoint /legacy-auth',
          'Modification des codes d\'erreur',
        ],
        steps: [
          'Mettre à jour les appels API pour utiliser le nouveau format',
          'Remplacer /legacy-auth par /auth/login',
          'Adapter la gestion des erreurs aux nouveaux codes',
        ],
        estimatedTime: '2-4 heures',
        resources: [
          '/docs/migration/v1-to-v2',
          '/examples/migration-v1-to-v2',
        ],
      },
      'v2.0.0-to-v3.0.0': {
        title: 'Migration de v2.0.0 vers v3.0.0',
        breakingChanges: [
          'Authentification JWT obligatoire',
          'Nouveau format de pagination',
          'Endpoints de paiement restructurés',
        ],
        steps: [
          'Implémenter l\'authentification JWT',
          'Adapter la pagination aux nouveaux paramètres',
          'Mettre à jour les appels de paiement',
        ],
        estimatedTime: '4-8 heures',
        resources: [
          '/docs/migration/v2-to-v3',
          '/examples/migration-v2-to-v3',
        ],
      },
      'v3.0.0-to-v3.8.1': {
        title: 'Migration de v3.0.0 vers v3.8.1',
        breakingChanges: [],
        newFeatures: [
          'Endpoints d\'analytics avancés',
          'Intégration Hanuman IA',
          'Système de recommandations',
        ],
        steps: [
          'Optionnel: Intégrer les nouveaux endpoints analytics',
          'Optionnel: Utiliser les recommandations IA',
        ],
        estimatedTime: '1-2 heures',
        resources: [
          '/docs/migration/v3-to-v3.8',
          '/examples/new-features-v3.8',
        ],
      },
    };
  }

  /**
   * 🔄 Obtenir les informations de compatibilité
   */
  private getBackwardCompatibilityInfo(): any {
    return {
      policy: 'Backward compatibility maintained for MINOR and PATCH versions',
      deprecationNotice: '6 months advance notice for breaking changes',
      supportWindow: '12 months for MAJOR versions',
      currentSupport: {
        'v3.x': {
          status: 'Active',
          endOfLife: '2025-12-31',
          securityUpdates: true,
          featureUpdates: true,
        },
        'v2.x': {
          status: 'Maintenance',
          endOfLife: '2024-12-31',
          securityUpdates: true,
          featureUpdates: false,
        },
        'v1.x': {
          status: 'Deprecated',
          endOfLife: '2024-06-30',
          securityUpdates: false,
          featureUpdates: false,
        },
      },
    };
  }

  /**
   * 📊 Obtenir les statistiques d'utilisation des versions
   */
  getVersionUsageStats(): any {
    // Dans un vrai système, ces données viendraient de l'analytics
    return {
      timestamp: new Date().toISOString(),
      totalRequests: 1000000,
      versionDistribution: {
        'v3.8.1': { requests: 650000, percentage: 65.0 },
        'v3.0.0': { requests: 250000, percentage: 25.0 },
        'v2.0.0': { requests: 80000, percentage: 8.0 },
        'v1.0.0': { requests: 20000, percentage: 2.0 },
      },
      trends: {
        'v3.8.1': 'increasing',
        'v3.0.0': 'stable',
        'v2.0.0': 'decreasing',
        'v1.0.0': 'deprecated',
      },
      recommendations: [
        'Encourager la migration de v2.0.0 vers v3.8.1',
        'Planifier la dépréciation de v2.0.0 pour Q4 2024',
        'Communiquer sur les nouvelles fonctionnalités v3.8.1',
      ],
    };
  }

  /**
   * 🎯 Générer un plan de migration personnalisé
   */
  generateMigrationPlan(currentVersion: string, targetVersion: string): any {
    const validation = this.validateVersion(currentVersion);
    if (!validation.isValid || !validation.isSupported) {
      return {
        error: validation.message,
      };
    }

    const targetValidation = this.validateVersion(targetVersion);
    if (!targetValidation.isValid || !targetValidation.isSupported) {
      return {
        error: `Version cible invalide: ${targetValidation.message}`,
      };
    }

    // Calculer le chemin de migration
    const migrationPath = this.calculateMigrationPath(currentVersion, targetVersion);
    
    return {
      from: currentVersion,
      to: targetVersion,
      migrationPath,
      estimatedTime: this.calculateMigrationTime(migrationPath),
      priority: this.calculateMigrationPriority(currentVersion, targetVersion),
      steps: this.generateMigrationSteps(migrationPath),
      resources: this.getMigrationResources(migrationPath),
      testing: this.getMigrationTestingStrategy(migrationPath),
    };
  }

  /**
   * 🛤️ Calculer le chemin de migration
   */
  private calculateMigrationPath(from: string, to: string): string[] {
    const fromIndex = this.supportedVersions.indexOf(from);
    const toIndex = this.supportedVersions.indexOf(to);
    
    if (fromIndex === -1 || toIndex === -1) {
      return [];
    }

    if (fromIndex < toIndex) {
      return this.supportedVersions.slice(fromIndex, toIndex + 1);
    } else {
      return [from, to]; // Downgrade (non recommandé)
    }
  }

  /**
   * ⏱️ Calculer le temps de migration
   */
  private calculateMigrationTime(path: string[]): string {
    if (path.length <= 1) return '0 heures';
    
    const baseTime = 2; // heures de base
    const complexityMultiplier = path.length - 1;
    const totalHours = baseTime * complexityMultiplier;
    
    return `${totalHours}-${totalHours * 2} heures`;
  }

  /**
   * 🎯 Calculer la priorité de migration
   */
  private calculateMigrationPriority(from: string, to: string): string {
    if (this.deprecatedVersions.includes(from)) {
      return 'CRITICAL';
    }
    
    const fromMajor = semver.major(from);
    const toMajor = semver.major(to);
    
    if (toMajor > fromMajor) {
      return 'HIGH';
    }
    
    return 'MEDIUM';
  }

  /**
   * 📝 Générer les étapes de migration
   */
  private generateMigrationSteps(path: string[]): any[] {
    const steps = [];
    
    for (let i = 0; i < path.length - 1; i++) {
      const from = path[i];
      const to = path[i + 1];
      
      steps.push({
        step: i + 1,
        from,
        to,
        description: `Migration de ${from} vers ${to}`,
        actions: this.getMigrationActions(from, to),
        validation: this.getMigrationValidation(from, to),
      });
    }
    
    return steps;
  }

  /**
   * 🔧 Obtenir les actions de migration
   */
  private getMigrationActions(from: string, to: string): string[] {
    // Actions génériques - dans un vrai système, cela serait plus spécifique
    return [
      `Sauvegarder la configuration actuelle (${from})`,
      `Tester les endpoints critiques avec la version ${to}`,
      `Mettre à jour les headers de version`,
      `Valider les réponses API`,
      `Déployer progressivement`,
    ];
  }

  /**
   * ✅ Obtenir la validation de migration
   */
  private getMigrationValidation(from: string, to: string): any {
    return {
      preChecks: [
        'Vérifier la compatibilité des dépendances',
        'Valider les tests automatisés',
        'Confirmer la disponibilité de rollback',
      ],
      postChecks: [
        'Vérifier les métriques de performance',
        'Valider les fonctionnalités critiques',
        'Confirmer les logs d\'erreur',
      ],
      rollbackPlan: `Retour à la version ${from} en cas de problème`,
    };
  }

  /**
   * 📚 Obtenir les ressources de migration
   */
  private getMigrationResources(path: string[]): any {
    return {
      documentation: path.map(version => `/docs/api/v${semver.major(version)}`),
      examples: path.map(version => `/examples/v${semver.major(version)}`),
      tools: [
        '/tools/version-checker',
        '/tools/migration-validator',
        '/tools/api-diff',
      ],
      support: {
        email: '<EMAIL>',
        slack: '#api-migration',
        documentation: '/docs/migration-support',
      },
    };
  }

  /**
   * 🧪 Obtenir la stratégie de test de migration
   */
  private getMigrationTestingStrategy(path: string[]): any {
    return {
      phases: [
        {
          name: 'Tests unitaires',
          description: 'Valider les changements de format',
          duration: '1-2 heures',
        },
        {
          name: 'Tests d\'intégration',
          description: 'Tester les flux complets',
          duration: '2-4 heures',
        },
        {
          name: 'Tests de charge',
          description: 'Valider les performances',
          duration: '1-2 heures',
        },
      ],
      environments: [
        'Development',
        'Staging',
        'Production (canary)',
        'Production (full)',
      ],
      metrics: [
        'Temps de réponse',
        'Taux d\'erreur',
        'Throughput',
        'Compatibilité',
      ],
    };
  }
}
