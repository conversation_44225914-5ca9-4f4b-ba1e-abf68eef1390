import { Injectable, Logger } from '@nestjs/common';
import { PerformanceObserver, performance } from 'perf_hooks';
import * as v8 from 'v8';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 🔧 Service d'Outils de Debug Avancés
 * Phase 4 - Excellence Opérationnelle
 */
@Injectable()
export class DebugToolsService {
  private readonly logger = new Logger(DebugToolsService.name);
  private performanceObserver: PerformanceObserver;
  private memoryLeakDetector: NodeJS.Timer;
  private performanceMetrics: Map<string, any[]> = new Map();
  private memorySnapshots: any[] = [];

  constructor() {
    this.initializePerformanceProfiler();
    this.initializeMemoryLeakDetector();
  }

  /**
   * 📊 Initialiser le profiler de performance
   */
  private initializePerformanceProfiler(): void {
    this.performanceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        this.recordPerformanceMetric(entry);
      });
    });

    this.performanceObserver.observe({ 
      entryTypes: ['measure', 'navigation', 'resource', 'mark'] 
    });

    this.logger.log('✅ Performance profiler initialized');
  }

  /**
   * 🧠 Initialiser le détecteur de fuites mémoire
   */
  private initializeMemoryLeakDetector(): void {
    this.memoryLeakDetector = setInterval(() => {
      this.captureMemorySnapshot();
      this.analyzeMemoryLeaks();
    }, 60000); // Toutes les minutes

    this.logger.log('✅ Memory leak detector initialized');
  }

  /**
   * ⏱️ Démarrer le profiling d'une opération
   */
  startProfiling(operationName: string): string {
    const markName = `${operationName}-start`;
    performance.mark(markName);
    return markName;
  }

  /**
   * ⏹️ Arrêter le profiling d'une opération
   */
  endProfiling(operationName: string, startMark: string): void {
    const endMark = `${operationName}-end`;
    const measureName = `${operationName}-duration`;
    
    performance.mark(endMark);
    performance.measure(measureName, startMark, endMark);
  }

  /**
   * 📈 Enregistrer une métrique de performance
   */
  private recordPerformanceMetric(entry: PerformanceEntry): void {
    const metricKey = entry.name;
    
    if (!this.performanceMetrics.has(metricKey)) {
      this.performanceMetrics.set(metricKey, []);
    }

    const metrics = this.performanceMetrics.get(metricKey);
    metrics.push({
      timestamp: Date.now(),
      duration: entry.duration,
      startTime: entry.startTime,
      entryType: entry.entryType,
    });

    // Garder seulement les 1000 dernières métriques
    if (metrics.length > 1000) {
      metrics.shift();
    }

    // Log des opérations lentes
    if (entry.duration > 1000) { // Plus de 1 seconde
      this.logger.warn(`🐌 Slow operation detected: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
    }
  }

  /**
   * 📸 Capturer un snapshot mémoire
   */
  private captureMemorySnapshot(): void {
    const memoryUsage = process.memoryUsage();
    const heapStatistics = v8.getHeapStatistics();
    
    const snapshot = {
      timestamp: Date.now(),
      memoryUsage,
      heapStatistics,
      heapSpaceStatistics: v8.getHeapSpaceStatistics(),
    };

    this.memorySnapshots.push(snapshot);

    // Garder seulement les 100 derniers snapshots
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots.shift();
    }
  }

  /**
   * 🔍 Analyser les fuites mémoire
   */
  private analyzeMemoryLeaks(): void {
    if (this.memorySnapshots.length < 10) return;

    const recent = this.memorySnapshots.slice(-10);
    const memoryGrowth = recent.map(s => s.memoryUsage.heapUsed);
    
    // Calculer la tendance de croissance
    const avgGrowth = this.calculateAverageGrowth(memoryGrowth);
    
    if (avgGrowth > 1024 * 1024) { // Plus de 1MB de croissance par minute
      this.logger.warn(`🚨 Potential memory leak detected: ${(avgGrowth / 1024 / 1024).toFixed(2)}MB/min growth`);
      this.generateMemoryLeakReport();
    }
  }

  /**
   * 📊 Calculer la croissance moyenne
   */
  private calculateAverageGrowth(values: number[]): number {
    if (values.length < 2) return 0;
    
    let totalGrowth = 0;
    for (let i = 1; i < values.length; i++) {
      totalGrowth += Math.max(0, values[i] - values[i - 1]);
    }
    
    return totalGrowth / (values.length - 1);
  }

  /**
   * 📋 Générer un rapport de performance
   */
  generatePerformanceReport(): any {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalOperations: 0,
        averageDuration: 0,
        slowOperations: 0,
        fastOperations: 0,
      },
      topSlowOperations: [],
      memoryStatus: this.getMemoryStatus(),
      recommendations: [],
    };

    // Analyser toutes les métriques
    let totalDuration = 0;
    let totalOperations = 0;
    const operationStats = new Map();

    this.performanceMetrics.forEach((metrics, operationName) => {
      const durations = metrics.map(m => m.duration);
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
      const maxDuration = Math.max(...durations);
      
      operationStats.set(operationName, {
        name: operationName,
        count: metrics.length,
        avgDuration,
        maxDuration,
        slowCount: durations.filter(d => d > 1000).length,
      });

      totalDuration += durations.reduce((a, b) => a + b, 0);
      totalOperations += metrics.length;
    });

    report.summary.totalOperations = totalOperations;
    report.summary.averageDuration = totalOperations > 0 ? totalDuration / totalOperations : 0;
    report.summary.slowOperations = Array.from(operationStats.values())
      .reduce((sum, stat) => sum + stat.slowCount, 0);
    report.summary.fastOperations = totalOperations - report.summary.slowOperations;

    // Top 10 des opérations les plus lentes
    report.topSlowOperations = Array.from(operationStats.values())
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, 10);

    // Générer des recommandations
    report.recommendations = this.generatePerformanceRecommendations(operationStats);

    return report;
  }

  /**
   * 🧠 Obtenir le statut mémoire
   */
  private getMemoryStatus(): any {
    const current = process.memoryUsage();
    const heap = v8.getHeapStatistics();
    
    return {
      current,
      heap,
      heapUsagePercent: (current.heapUsed / heap.heap_size_limit) * 100,
      trend: this.memorySnapshots.length > 1 ? 
        this.calculateMemoryTrend() : 'insufficient_data',
    };
  }

  /**
   * 📈 Calculer la tendance mémoire
   */
  private calculateMemoryTrend(): string {
    if (this.memorySnapshots.length < 5) return 'insufficient_data';
    
    const recent = this.memorySnapshots.slice(-5);
    const growth = recent[recent.length - 1].memoryUsage.heapUsed - recent[0].memoryUsage.heapUsed;
    
    if (growth > 10 * 1024 * 1024) return 'increasing'; // Plus de 10MB
    if (growth < -5 * 1024 * 1024) return 'decreasing'; // Moins de 5MB
    return 'stable';
  }

  /**
   * 💡 Générer des recommandations de performance
   */
  private generatePerformanceRecommendations(operationStats: Map<string, any>): string[] {
    const recommendations = [];
    
    operationStats.forEach((stat) => {
      if (stat.avgDuration > 2000) {
        recommendations.push(`🐌 Optimiser "${stat.name}": durée moyenne ${stat.avgDuration.toFixed(2)}ms`);
      }
      
      if (stat.slowCount > stat.count * 0.1) {
        recommendations.push(`⚠️ "${stat.name}" a ${stat.slowCount} opérations lentes sur ${stat.count}`);
      }
    });

    const memoryStatus = this.getMemoryStatus();
    if (memoryStatus.heapUsagePercent > 80) {
      recommendations.push('🧠 Utilisation mémoire élevée: considérer l\'optimisation ou l\'augmentation des ressources');
    }

    if (memoryStatus.trend === 'increasing') {
      recommendations.push('📈 Tendance croissante de la mémoire: vérifier les fuites potentielles');
    }

    return recommendations;
  }

  /**
   * 📄 Générer un rapport de fuite mémoire
   */
  private generateMemoryLeakReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      memorySnapshots: this.memorySnapshots.slice(-20),
      analysis: this.analyzeMemoryPattern(),
      recommendations: this.generateMemoryRecommendations(),
    };

    // Sauvegarder le rapport
    const reportsDir = path.join(process.cwd(), 'debug-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const filename = `memory-leak-${Date.now()}.json`;
    fs.writeFileSync(
      path.join(reportsDir, filename),
      JSON.stringify(report, null, 2)
    );

    this.logger.warn(`📄 Memory leak report generated: ${filename}`);
  }

  /**
   * 🔍 Analyser le pattern mémoire
   */
  private analyzeMemoryPattern(): any {
    const recent = this.memorySnapshots.slice(-20);
    const heapUsages = recent.map(s => s.memoryUsage.heapUsed);
    
    return {
      trend: this.calculateMemoryTrend(),
      averageGrowth: this.calculateAverageGrowth(heapUsages),
      peakUsage: Math.max(...heapUsages),
      minUsage: Math.min(...heapUsages),
      volatility: this.calculateVolatility(heapUsages),
    };
  }

  /**
   * 📊 Calculer la volatilité
   */
  private calculateVolatility(values: number[]): number {
    if (values.length < 2) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length;
    
    return Math.sqrt(variance);
  }

  /**
   * 💡 Générer des recommandations mémoire
   */
  private generateMemoryRecommendations(): string[] {
    return [
      'Vérifier les closures et event listeners non nettoyés',
      'Analyser les caches qui ne sont pas vidés',
      'Vérifier les timers et intervals non clearés',
      'Examiner les références circulaires',
      'Considérer l\'utilisation de WeakMap/WeakSet',
    ];
  }

  /**
   * 🧹 Nettoyer les ressources
   */
  cleanup(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
    
    if (this.memoryLeakDetector) {
      clearInterval(this.memoryLeakDetector);
    }
    
    this.logger.log('✅ Debug tools cleaned up');
  }
}
