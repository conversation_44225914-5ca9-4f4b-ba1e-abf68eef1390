import { Module, OnModuleInit } from '@nestjs/common';
import { InteractiveSwaggerService } from './documentation/interactive-swagger.service';
import { DebugToolsService } from './debug/debug-tools.service';
import { AdminInterfaceService } from './admin/admin-interface.service';
import { ApiVersioningService } from './versioning/api-versioning.service';
import { Phase4Controller } from './phase4.controller';

/**
 * 🎯 Module Phase 4 - Excellence Opérationnelle
 * Intégration complète des outils d'excellence opérationnelle
 */
@Module({
  providers: [
    InteractiveSwaggerService,
    DebugToolsService,
    AdminInterfaceService,
    ApiVersioningService,
  ],
  controllers: [Phase4Controller],
  exports: [
    InteractiveSwaggerService,
    DebugToolsService,
    AdminInterfaceService,
    ApiVersioningService,
  ],
})
export class Phase4ExcellenceModule implements OnModuleInit {
  constructor(
    private readonly debugToolsService: DebugToolsService,
  ) {}

  async onModuleInit() {
    console.log('🚀 Phase 4 Excellence Opérationnelle Module initialized');
  }

  onModuleDestroy() {
    // Nettoyer les ressources
    this.debugToolsService.cleanup();
    console.log('🧹 Phase 4 Excellence Opérationnelle Module cleaned up');
  }
}
