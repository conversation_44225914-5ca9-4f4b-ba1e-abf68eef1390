import { Injectable, Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 📚 Service de Documentation Interactive Enrichie
 * Phase 4 - Excellence Opérationnelle
 */
@Injectable()
export class InteractiveSwaggerService {
  private readonly logger = new Logger(InteractiveSwaggerService.name);

  /**
   * Configuration Swagger UI enrichie avec exemples interactifs
   */
  setupInteractiveDocumentation(app: INestApplication): void {
    this.logger.log('Setting up interactive Swagger documentation...');

    const config = new DocumentBuilder()
      .setTitle('🚀 Retreat And Be API - Documentation Interactive')
      .setDescription(this.getEnhancedDescription())
      .setVersion('3.8.1')
      .setContact(
        'Équipe Technique',
        'https://retreatandbe.com/contact',
        '<EMAIL>'
      )
      .setLicense('MIT', 'https://opensource.org/licenses/MIT')
      .addServer('http://localhost:3000', 'Développement Local')
      .addServer('https://api-staging.retreatandbe.com', 'Staging')
      .addServer('https://api.retreatandbe.com', 'Production')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Token JWT pour authentification',
          in: 'header',
        },
        'JWT-auth'
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-API-Key',
          in: 'header',
          description: 'Clé API pour accès programmatique',
        },
        'API-Key'
      )
      .addTag('🔐 Authentication', 'Gestion de l\'authentification et autorisation')
      .addTag('👤 Users', 'Gestion des utilisateurs et profils')
      .addTag('🏠 Retreats', 'Gestion des retraites et réservations')
      .addTag('💳 Payments', 'Système de paiement et facturation')
      .addTag('📊 Analytics', 'Métriques et analyses business')
      .addTag('🤖 AI Services', 'Services d\'intelligence artificielle')
      .addTag('🔧 System', 'Endpoints système et monitoring')
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [],
      deepScanRoutes: true,
    });

    // Enrichir le document avec des exemples personnalisés
    this.enrichDocumentWithExamples(document);

    // Configuration Swagger UI avec thème personnalisé
    const swaggerOptions = {
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
        requestInterceptor: this.getRequestInterceptor(),
        responseInterceptor: this.getResponseInterceptor(),
        onComplete: this.getOnCompleteCallback(),
        docExpansion: 'list',
        defaultModelsExpandDepth: 2,
        defaultModelExpandDepth: 2,
        displayOperationId: true,
        showMutatedRequest: true,
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
      },
      customCss: this.getCustomCSS(),
      customJs: this.getCustomJS(),
      customfavIcon: '/assets/favicon.ico',
      customSiteTitle: 'Retreat And Be API - Documentation Interactive',
    };

    SwaggerModule.setup('api/docs', app, document, swaggerOptions);

    // Générer la documentation statique
    this.generateStaticDocumentation(document);

    this.logger.log('✅ Interactive Swagger documentation setup completed');
  }

  /**
   * Description enrichie de l'API
   */
  private getEnhancedDescription(): string {
    return `
## 🌟 API Retreat And Be - Plateforme de Retraites Spirituelles

### 🎯 Vue d'ensemble
Cette API REST moderne permet de gérer une plateforme complète de retraites spirituelles avec des fonctionnalités avancées d'IA et d'analytics.

### 🚀 Fonctionnalités Principales
- **Authentification JWT** avec refresh tokens et RBAC
- **Système de recommandations IA** personnalisées
- **Paiements sécurisés** avec Stripe et PayPal
- **Analytics temps réel** avec métriques business
- **Intégration Hanuman** (IA distribuée)
- **Cache Redis** pour performances optimales

### 📖 Guides d'Intégration
- [Guide de Démarrage Rapide](/api/docs/guides/quick-start)
- [Authentification](/api/docs/guides/authentication)
- [Webhooks](/api/docs/guides/webhooks)
- [Rate Limiting](/api/docs/guides/rate-limiting)
- [Codes d'Erreur](/api/docs/guides/error-codes)

### 🔧 Outils de Développement
- **Postman Collection**: [Télécharger](/api/docs/postman)
- **SDK JavaScript**: [Documentation](/api/docs/sdk/js)
- **SDK Python**: [Documentation](/api/docs/sdk/python)
- **Exemples de Code**: [GitHub Repository](https://github.com/retreatandbe/api-examples)

### 📊 Monitoring & Support
- **Status Page**: [status.retreatandbe.com](https://status.retreatandbe.com)
- **Support Technique**: <EMAIL>
- **Documentation**: [docs.retreatandbe.com](https://docs.retreatandbe.com)
    `;
  }

  /**
   * Enrichir le document avec des exemples personnalisés
   */
  private enrichDocumentWithExamples(document: any): void {
    // Ajouter des exemples pour les endpoints principaux
    const examples = {
      '/auth/login': {
        requestBody: {
          email: '<EMAIL>',
          password: 'SecurePassword123!',
          rememberMe: true,
        },
        responses: {
          200: {
            accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            user: {
              id: '123e4567-e89b-12d3-a456-426614174000',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Doe',
              role: 'CLIENT',
            },
          },
        },
      },
      '/retreats': {
        responses: {
          200: [
            {
              id: '123e4567-e89b-12d3-a456-426614174000',
              title: 'Retraite Méditation Pleine Conscience',
              description: 'Une retraite transformatrice de 7 jours...',
              price: 899.99,
              currency: 'EUR',
              startDate: '2024-06-15T10:00:00Z',
              endDate: '2024-06-22T16:00:00Z',
              location: {
                name: 'Centre Spirituel des Alpes',
                address: '123 Route de la Montagne, 74000 Annecy',
                coordinates: { lat: 45.8992, lng: 6.1294 },
              },
              instructor: {
                id: '456e7890-e89b-12d3-a456-426614174000',
                name: 'Marie Dubois',
                bio: 'Instructrice certifiée avec 15 ans d\'expérience...',
              },
              availableSpots: 8,
              totalSpots: 12,
            },
          ],
        },
      },
    };

    // Intégrer les exemples dans le document
    Object.keys(examples).forEach((path) => {
      if (document.paths[path]) {
        Object.keys(document.paths[path]).forEach((method) => {
          const operation = document.paths[path][method];
          if (examples[path].requestBody && operation.requestBody) {
            operation.requestBody.content['application/json'].example =
              examples[path].requestBody;
          }
          if (examples[path].responses) {
            Object.keys(examples[path].responses).forEach((statusCode) => {
              if (operation.responses[statusCode]) {
                operation.responses[statusCode].content = {
                  'application/json': {
                    example: examples[path].responses[statusCode],
                  },
                };
              }
            });
          }
        });
      }
    });
  }

  /**
   * CSS personnalisé pour Swagger UI
   */
  private getCustomCSS(): string {
    return `
      .swagger-ui .topbar { 
        background-color: #2c3e50; 
        border-bottom: 3px solid #3498db;
      }
      .swagger-ui .topbar .download-url-wrapper { 
        display: none; 
      }
      .swagger-ui .info .title {
        color: #2c3e50;
        font-size: 2.5em;
        font-weight: bold;
      }
      .swagger-ui .info .description {
        font-size: 1.1em;
        line-height: 1.6;
      }
      .swagger-ui .scheme-container {
        background: #ecf0f1;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
      }
      .swagger-ui .opblock.opblock-post {
        border-color: #27ae60;
        background: rgba(39, 174, 96, 0.1);
      }
      .swagger-ui .opblock.opblock-get {
        border-color: #3498db;
        background: rgba(52, 152, 219, 0.1);
      }
      .swagger-ui .opblock.opblock-put {
        border-color: #f39c12;
        background: rgba(243, 156, 18, 0.1);
      }
      .swagger-ui .opblock.opblock-delete {
        border-color: #e74c3c;
        background: rgba(231, 76, 60, 0.1);
      }
      .swagger-ui .btn.authorize {
        background-color: #3498db;
        border-color: #3498db;
      }
      .swagger-ui .btn.authorize:hover {
        background-color: #2980b9;
        border-color: #2980b9;
      }
    `;
  }

  /**
   * JavaScript personnalisé pour Swagger UI
   */
  private getCustomJS(): string {
    return `
      window.onload = function() {
        // Ajouter des fonctionnalités personnalisées
        console.log('🚀 Retreat And Be API Documentation Loaded');
        
        // Ajouter un bouton de test rapide
        setTimeout(() => {
          const topbar = document.querySelector('.topbar');
          if (topbar) {
            const testButton = document.createElement('button');
            testButton.innerHTML = '🧪 Test Rapide';
            testButton.className = 'btn btn-primary';
            testButton.style.marginLeft = '20px';
            testButton.onclick = () => {
              alert('Fonctionnalité de test rapide - À implémenter');
            };
            topbar.appendChild(testButton);
          }
        }, 1000);
      };
    `;
  }

  /**
   * Intercepteur de requêtes pour logging
   */
  private getRequestInterceptor(): string {
    return `
      (request) => {
        console.log('📤 API Request:', request);
        return request;
      }
    `;
  }

  /**
   * Intercepteur de réponses pour logging
   */
  private getResponseInterceptor(): string {
    return `
      (response) => {
        console.log('📥 API Response:', response);
        return response;
      }
    `;
  }

  /**
   * Callback de completion
   */
  private getOnCompleteCallback(): string {
    return `
      () => {
        console.log('✅ Swagger UI Loaded Successfully');
      }
    `;
  }

  /**
   * Générer la documentation statique
   */
  private generateStaticDocumentation(document: any): void {
    try {
      const docsDir = path.join(process.cwd(), 'docs', 'api');
      if (!fs.existsSync(docsDir)) {
        fs.mkdirSync(docsDir, { recursive: true });
      }

      // Sauvegarder le document OpenAPI
      fs.writeFileSync(
        path.join(docsDir, 'openapi.json'),
        JSON.stringify(document, null, 2)
      );

      this.logger.log('✅ Static documentation generated');
    } catch (error) {
      this.logger.error('❌ Failed to generate static documentation', error);
    }
  }
}
