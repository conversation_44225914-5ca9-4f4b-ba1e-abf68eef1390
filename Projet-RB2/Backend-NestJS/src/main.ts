import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from './prisma/prisma.service';
import { InteractiveSwaggerService } from './modules/phase4-excellence/documentation/interactive-swagger.service';
import { writeFileSync } from 'fs';
import { join } from 'path';

import { validateEnv } from './config/validate-env';

// Validation stricte de la configuration .env avec Zod
validateEnv(process.env);

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');

  // Get environment variables
  const port = configService.get<number>('app.port', 3000);
  const apiPrefix = configService.get<string>('app.apiPrefix', '/api/v1');
  const nodeEnv = configService.get<string>('app.nodeEnv', 'development');

  // Set global prefix
  app.setGlobalPrefix(apiPrefix);

  // Enable CORS
  app.enableCors(configService.get('security.cors'));

  // Use Helmet for security headers
  app.use(helmet(configService.get('security.helmet')));

  // Use compression
  app.use(compression());

  // Enable validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Enable Prisma shutdown hook
  const prismaService = app.get(PrismaService);
  await prismaService.enableShutdownHooks(app);

  // Setup Phase 4 Interactive Swagger Documentation
  try {
    const interactiveSwaggerService = app.get(InteractiveSwaggerService);
    interactiveSwaggerService.setupInteractiveDocumentation(app);
    logger.log('✅ Phase 4 Interactive Swagger documentation configured');
  } catch (error) {
    logger.warn('⚠️ Phase 4 Interactive Swagger service not available, using fallback');

    // Fallback to basic Swagger setup
    const config = new DocumentBuilder()
      .setTitle('Retreat And Be API')
      .setDescription('The Retreat And Be API documentation')
      .setVersion('3.8.1')
      .addBearerAuth()
      .addTag('auth', 'Authentication endpoints')
      .addTag('users', 'User management endpoints')
      .addTag('retreats', 'Retreat management endpoints')
      .addTag('bookings', 'Booking management endpoints')
      .addTag('payments', 'Payment endpoints')
      .addTag('analytics', 'Analytics endpoints')
      .addTag('phase4', 'Phase 4 Excellence Opérationnelle')
      .addTag('health', 'Health check endpoints')
      .build();

    const document = SwaggerModule.createDocument(app, config);

    // Save Swagger JSON file
    if (nodeEnv !== 'production') {
      writeFileSync(
        join(process.cwd(), 'swagger-spec.json'),
        JSON.stringify(document, null, 2),
      );
      logger.log('Swagger JSON file generated');
    }

    SwaggerModule.setup('api/docs', app, document);
  }

  logger.log('📚 Swagger documentation is available at: /api/docs');

  // Start the server
  await app.listen(port);
  logger.log(`Application is running in ${nodeEnv} mode`);
  logger.log(`Server is listening on port ${port}`);
  logger.log(`API is available at: ${apiPrefix}`);
}

bootstrap().catch((error) => {
  console.error('Application failed to start:', error);
  process.exit(1);
});
