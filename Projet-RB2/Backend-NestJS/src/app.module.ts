import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { ActivitiesModule } from './modules/activities/activities.module';
import { GamificationModule } from './modules/gamification/gamification.module';
import { LearningModule } from './modules/learning/learning.module';
import { EventsModule } from './modules/events/events.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { SecurityModule } from './modules/security/security.module';
import { AuditModule } from './modules/audit/audit.module';
import { IntegrationModule } from './modules/integration/integration.module';
import { RecommendationModule } from './modules/recommendation/recommendation.module';
import { CouponModule } from './modules/coupon/coupon.module';
import { PerformanceModule } from './modules/performance/performance.module';
import { HealthModule } from './health/health.module';
import { MonitoringModule } from './modules/monitoring/monitoring.module';
import { RetreatsModule } from './modules/retreats/retreats.module';
import { BookingsModule } from './modules/bookings/bookings.module';
import { PaymentsModule } from './modules/payments/payments.module';
import { MessagingModule } from './modules/messaging/messaging.module';
import { PartnersModule } from './modules/partners/partners.module';
import { FilesModule } from './modules/files/files.module';
import { MatchingModule } from './modules/matching/matching.module';
import { SocialModule } from './modules/social/social.module';
import { ModerationModule } from './modules/moderation/moderation.module';
import { AnalyticsModule } from './modules/analytics/analytics.module';
import { CacheModule } from './cache/cache.module';
import { ResilienceModule } from './modules/resilience/resilience.module';
import { Phase4ExcellenceModule } from './modules/phase4-excellence/phase4-excellence.module';
import { ValidationModule } from './modules/validation/validation.module';
import { CommonModule } from './common/common.module';
import { SharedModule } from './shared/shared.module';
import { I18nModule } from './i18n/i18n.module';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { CacheInterceptor } from './common/interceptors/cache.interceptor';
import { LanguageInterceptor } from './i18n/interceptors/language.interceptor';
import { JwtAuthGuard } from './modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from './modules/auth/guards/roles.guard';
import configurations from './config';

// Middleware
import { GlobalRateLimiterMiddleware } from './middleware/global-rate-limiter.middleware';
import { CsrfMiddleware } from './middleware/csrf.middleware';

@Module({
  imports: [
    // Configuration globale
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV || 'development'}`,
      load: configurations,
      cache: true,
      expandVariables: true,
    }),

    // Module Prisma pour la connexion à la base de données
    PrismaModule,

    // Modules de base
    CommonModule,
    SharedModule,

    // Module de cache Redis
    CacheModule,

    // Module de résilience (Circuit Breakers, Retry, Health Checks)
    ResilienceModule,

    // Module de validation avancée (Joi, Sanitization, Security)
    ValidationModule,

    // Module d'internationalisation
    I18nModule,

    // Modules fonctionnels
    AuthModule,
    UsersModule,
    RetreatsModule,
    BookingsModule,
    PaymentsModule,
    ActivitiesModule,
    GamificationModule,
    LearningModule,
    EventsModule,
    NotificationsModule,
    SecurityModule,
    AuditModule,
    IntegrationModule,
    RecommendationModule,
    CouponModule,
    PerformanceModule,
    HealthModule,
    MonitoringModule,
    MessagingModule,
    PartnersModule,
    FilesModule,
    MatchingModule,
    SocialModule,
    ModerationModule,
    AnalyticsModule,

    // Module Phase 4 - Excellence Opérationnelle
    Phase4ExcellenceModule,
  ],
  providers: [
    // Filtre global pour gérer toutes les exceptions
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },

    // Intercepteur global pour la journalisation
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },

    // Intercepteur global pour le cache
    {
      provide: APP_INTERCEPTOR,
      useClass: CacheInterceptor,
    },

    // Intercepteur global pour la détection de la langue
    {
      provide: APP_INTERCEPTOR,
      useClass: LanguageInterceptor,
    },

    // Guard global pour l'authentification JWT
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },

    // Guard global pour les rôles
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(GlobalRateLimiterMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });

    consumer
      .apply(CsrfMiddleware)
      .exclude(
        { path: 'api/docs*', method: RequestMethod.ALL },
        { path: 'api/v1/health', method: RequestMethod.ALL }
      )
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
